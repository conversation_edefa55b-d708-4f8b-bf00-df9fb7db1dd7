<template>
  <div>
    <el-form
      :inline="true"
      :model="form${className}"
      ref="form"
      :size="layoutStore.defaultFormItemSize"
      @submit.prevent
    >
      <filter-box :item-width="350" @search="refreshForm${className}(true)" @reset="onReset">
<#list queryColumns as column>
<#if column.queryType == "LIKE" || column.queryType == "EQ">
        <el-form-item prop="formFilter.${column.javaField}">
<#if column.dictType?? && column.dictType != "">
          <el-select
            class="filter-item"
            v-model="form${className}.formFilter.${column.javaField}"
            :clearable="true"
            placeholder="${column.columnComment}"
          >
            <el-option
              v-for="item in ${column.javaField}DictList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
<#elseif column.javaType == "String">
          <el-input
            class="filter-item"
            v-model="form${className}.formFilter.${column.javaField}"
            :clearable="true"
            placeholder="${column.columnComment}"
          />
<#elseif column.javaType == "Integer" || column.javaType == "Long">
          <el-input-number
            class="filter-item"
            v-model="form${className}.formFilter.${column.javaField}"
            placeholder="${column.columnComment}"
            :clearable="true"
          />
<#elseif column.javaType == "Date">
          <el-date-picker
            class="filter-item"
            v-model="form${className}.formFilter.${column.javaField}"
            type="date"
            placeholder="${column.columnComment}"
            :clearable="true"
          />
<#else>
          <el-input
            class="filter-item"
            v-model="form${className}.formFilter.${column.javaField}"
            :clearable="true"
            placeholder="${column.columnComment}"
          />
</#if>
        </el-form-item>
<#elseif column.queryType == "BETWEEN">
<#if column.javaType == "Date">
        <el-form-item prop="formFilter.${column.javaField}Range">
          <el-date-picker
            class="filter-item"
            v-model="form${className}.formFilter.${column.javaField}Range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始${column.columnComment}"
            end-placeholder="结束${column.columnComment}"
            :clearable="true"
            @change="onDateRangeChange('${column.javaField}', $event)"
          />
        </el-form-item>
<#else>
        <el-form-item prop="formFilter.${column.javaField}Start">
          <div class="range-input">
<#if column.javaType == "Integer" || column.javaType == "Long" || column.javaType == "BigDecimal">
            <el-input-number
              v-model="form${className}.formFilter.${column.javaField}Start"
              placeholder="最小${column.columnComment}"
              :clearable="true"
              style="width: 120px"
            />
            <span class="range-separator">至</span>
            <el-input-number
              v-model="form${className}.formFilter.${column.javaField}End"
              placeholder="最大${column.columnComment}"
              :clearable="true"
              style="width: 120px"
            />
<#else>
            <el-input
              v-model="form${className}.formFilter.${column.javaField}Start"
              placeholder="开始${column.columnComment}"
              :clearable="true"
              style="width: 120px"
            />
            <span class="range-separator">至</span>
            <el-input
              v-model="form${className}.formFilter.${column.javaField}End"
              placeholder="结束${column.columnComment}"
              :clearable="true"
              style="width: 120px"
            />
</#if>
          </div>
        </el-form-item>
</#if>
</#if>
</#list>
      </filter-box>
    </el-form>

    <table-box
      class="page-table"
      ref="${className?lower_case}"
      :data="form${className}.${className?lower_case}.impl.dataList"
      :seq-config="{
        startIndex: (form${className}.${className?lower_case}.impl.currentPage - 1) * form${className}.${className?lower_case}.impl.pageSize,
      }"
      :size="layoutStore.defaultFormItemSize"
      @sort-change="form${className}.${className?lower_case}.impl.onSortChange"
      @refresh="refreshForm${className}(true)"
    >
      <template v-slot:operator>
        <el-button
          type="primary"
          :size="layoutStore.defaultFormItemSize"
          :disabled="!checkPermCodeExist('form${className}:fragment${className}:add')"
          @click="onFormAdd${className}Click()"
        >
          新建
        </el-button>
      </template>

      <vxe-column
        type="seq"
        width="55px"
        :index="form${className}.${className?lower_case}.impl.getTableIndex"
      />

<#list listColumns as column>
      <vxe-column title="${column.columnComment}" field="<#if column.dictType?? && column.dictType != "">${column.javaField}DictMap.name<#else>${column.javaField}</#if>">
<#if column.javaType == "Date" && (column.dictType?? == false || column.dictType == "")>
        <template v-slot="scope">
          <span>{{ formatDate(scope.row.${column.javaField}) }}</span>
        </template>
</#if>
      </vxe-column>
</#list>

      <vxe-column title="操作" fixed="right" width="180px">
        <template v-slot="scope">
          <el-button
            @click.stop="onFormEdit${className}Click(scope.row)"
            type="primary"
            link
            :size="layoutStore.defaultFormItemSize"
            :disabled="!checkPermCodeExist('form${className}:fragment${className}:update')"
          >
            编辑
          </el-button>
          <el-button
            @click.stop="onDeleteClick(scope.row)"
            link
            :size="layoutStore.defaultFormItemSize"
            type="danger"
            :disabled="!checkPermCodeExist('form${className}:fragment${className}:delete')"
          >
            删除
          </el-button>
        </template>
      </vxe-column>

      <template v-slot:pagination>
        <el-row justify="end" style="margin-top: 16px">
          <el-pagination
            :total="form${className}.${className?lower_case}.impl.totalCount"
            :current-page="form${className}.${className?lower_case}.impl.currentPage"
            :page-size="form${className}.${className?lower_case}.impl.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, prev, pager, next, sizes"
            @current-change="form${className}.${className?lower_case}.impl.onCurrentPageChange"
            @size-change="form${className}.${className?lower_case}.impl.onPageSizeChange"
          >
          </el-pagination>
        </el-row>
      </template>
    </table-box>
  </div>
</template>

<script lang="ts">
export default {
  name: 'form${className}',
};
</script>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useTable } from '@/common/hooks/useTable';
import { TableOptions } from '@/common/types/pagination';
import { TableData } from '@/common/types/table';
import { ANY_OBJECT } from '@/types/generic';
import { ${className} } from '@/types/${moduleName}/${className?lower_case}';
import ${className}Controller from '@/api/${moduleName}/${className}Controller';
import { useDialog } from '@/components/Dialog/useDialog';
import { useLayoutStore } from '@/store';
import { usePermissions } from '@/common/hooks/usePermission';
import FormEdit${className} from '../formEdit${className}/index.vue';
<#list queryColumns as column>
<#if column.dictType?? && column.dictType != "">
import { DictionaryController } from '@/api/system';
<#break>
</#if>
</#list>

const layoutStore = useLayoutStore();
const Dialog = useDialog();
const { checkPermCodeExist } = usePermissions();
const form = ref();

<#list queryColumns as column>
<#if column.dictType?? && column.dictType != "">
// ${column.columnComment}字典数据
const ${column.javaField}DictList = ref<Array<{id: any, name: string}>>([]);
</#if>
</#list>

/**
 * ${functionName}数据获取函数，返回Promise
 */
const load${className}WidgetData = (params: ANY_OBJECT): Promise<TableData<${className}>> => {
  if (params == null) params = {};
  params = {
    ...params,
    ${className?lower_case}DtoFilter: {
<#list queryColumns as column>
<#if column.queryType == "BETWEEN">
      ${column.javaField}Start: form${className}.formFilterCopy.${column.javaField}Start,
      ${column.javaField}End: form${className}.formFilterCopy.${column.javaField}End,
<#else>
      ${column.javaField}: form${className}.formFilterCopy.${column.javaField},
</#if>
</#list>
    },
  };
  return new Promise((resolve, reject) => {
    ${className}Controller.list(params)
      .then(res => {
        resolve({
          dataList: res.data.dataList,
          totalCount: res.data.totalCount,
        });
      })
      .catch(e => {
        reject(e);
      });
  });
};

/**
 * ${functionName}数据获取检测函数，返回true正常获取数据，返回false停止获取数据
 */
const load${className}Verify = () => {
<#list queryColumns as column>
<#if column.queryType == "BETWEEN">
  form${className}.formFilterCopy.${column.javaField}Start = form${className}.formFilter.${column.javaField}Start;
  form${className}.formFilterCopy.${column.javaField}End = form${className}.formFilter.${column.javaField}End;
<#else>
  form${className}.formFilterCopy.${column.javaField} = form${className}.formFilter.${column.javaField};
</#if>
</#list>
  return true;
};

const tableOptions: TableOptions<${className}> = {
  loadTableData: load${className}WidgetData,
  verifyTableParameter: load${className}Verify,
  paged: true,
  orderFieldName: '${pkColumn.javaField}',
  ascending: false,
};

const form${className} = reactive({
  formFilter: {
<#list queryColumns as column>
<#if column.queryType == "BETWEEN">
<#if column.javaType == "Date">
    ${column.javaField}Range: undefined,
</#if>
    ${column.javaField}Start: undefined,
    ${column.javaField}End: undefined,
<#else>
    ${column.javaField}: undefined,
</#if>
</#list>
  },
  formFilterCopy: {
<#list queryColumns as column>
<#if column.queryType == "BETWEEN">
    ${column.javaField}Start: undefined,
    ${column.javaField}End: undefined,
<#else>
    ${column.javaField}: undefined,
</#if>
</#list>
  },
  ${className?lower_case}: {
    impl: useTable(tableOptions),
  },
});

const onReset = () => {
  form.value.resetFields();
  refreshForm${className}(true);
};

const refreshForm${className} = (reloadData = false) => {
  if (reloadData) {
    form${className}.${className?lower_case}.impl.refreshTable(true, 1);
  } else {
    form${className}.${className?lower_case}.impl.refreshTable();
  }
};

// 日期范围变化处理
const onDateRangeChange = (fieldName: string, dateRange: any) => {
  if (dateRange && dateRange.length === 2) {
    form${className}.formFilter[`${"$"}{fieldName}Start`] = dateRange[0];
    form${className}.formFilter[`${"$"}{fieldName}End`] = dateRange[1];
  } else {
    form${className}.formFilter[`${"$"}{fieldName}Start`] = undefined;
    form${className}.formFilter[`${"$"}{fieldName}End`] = undefined;
  }
};

<#-- 计算弹窗大小 -->
<#assign nonPkColumns = 0>
<#list formColumns as column>
<#if !column.isPk>
<#assign nonPkColumns = nonPkColumns + 1>
</#if>
</#list>

<#-- 根据字段数量计算列数 -->
<#assign dialogColumnCount = 1>
<#if (nonPkColumns > 10)>
<#assign dialogColumnCount = 2>
</#if>
<#if (nonPkColumns > 20)>
<#assign dialogColumnCount = 3>
</#if>
<#if (nonPkColumns > 30)>
<#assign dialogColumnCount = 4>
</#if>

<#-- 计算行数 -->
<#assign rowsNeeded = (nonPkColumns + dialogColumnCount - 1) / dialogColumnCount>
<#assign rowsNeeded = rowsNeeded?int>

<#-- 根据列数设置宽度 -->
<#assign dialogWidth = "600px">
<#if (dialogColumnCount == 2)>
<#assign dialogWidth = "800px">
<#elseif (dialogColumnCount == 3)>
<#assign dialogWidth = "1000px">
<#elseif (dialogColumnCount >= 4)>
<#assign dialogWidth = "1200px">
</#if>

// 弹窗大小
const dialogSize = ['${dialogWidth}'];

// 新增
const onFormAdd${className}Click = () => {
  Dialog.show(
    '新增${functionName}',
    FormEdit${className},
    {
      area: [dialogSize.width],
    },
    {}
  )
    .then(() => {
      refreshForm${className}();
    })
    .catch(e => {});
};

// 编辑
const onFormEdit${className}Click = (row: ${className}) => {
  Dialog.show(
    '编辑${functionName}',
    FormEdit${className},
    {
      area: [dialogSize.width],
    },
    {${pkColumn.javaField}: row.${pkColumn.javaField}}
  )
    .then(() => {
      refreshForm${className}();
    })
    .catch(e => {});
};

// 删除
const onDeleteClick = (row: ${className}) => {
  ElMessageBox.confirm(`是否删除${functionName}【${"$"}{row.<#if listColumns?? && listColumns?size gt 0>${listColumns[0].javaField}<#else>${pkColumn.javaField}</#if>}】？`, '', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      ${className}Controller.delete({${pkColumn.javaField}: row.${pkColumn.javaField}})
        .then(() => {
          ElMessage.success('删除成功');
          refreshForm${className}();
        })
        .catch(e => {});
    })
    .catch(e => {});
};

// 日期格式化
const formatDate = (date: any) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString();
};

<#list queryColumns as column>
<#if column.dictType?? && column.dictType != "">
/**
 * 加载${column.columnComment}字典数据
 */
const load${column.javaField?cap_first}Dict = () => {
  DictionaryController.dictGlobalDict({ dictCode: '${column.dictType}' })
    .then(res => {
      console.log('字典数据加载结果:', res);
      ${column.javaField}DictList.value = res.getList() || [];
    })
    .catch(e => {
      console.warn('加载${column.columnComment}字典失败:', e);
    });
};

</#if>
</#list>
onMounted(() => {
  refreshForm${className}(true);
<#list queryColumns as column>
<#if column.dictType?? && column.dictType != "">
  load${column.javaField?cap_first}Dict();
</#if>
</#list>
});
</script>

<style scoped>
.range-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #909399;
  font-size: 12px;
}
</style>
