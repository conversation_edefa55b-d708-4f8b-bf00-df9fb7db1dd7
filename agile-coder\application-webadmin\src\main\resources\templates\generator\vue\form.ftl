<#-- 计算表单字段总数和布局 -->
<#assign nonPkColumns = 0>
<#list formColumns as column>
<#if !column.isPk>
<#assign nonPkColumns = nonPkColumns + 1>
</#if>
</#list>

<#-- 根据字段数量计算列数 -->
<#assign columnCount = 1>
<#if (nonPkColumns > 10)>
<#assign columnCount = 2>
</#if>
<#if (nonPkColumns > 20)>
<#assign columnCount = 3>
</#if>
<#if (nonPkColumns > 30)>
<#assign columnCount = 4>
</#if>

<#-- 计算span值 -->
<#assign columnSpan = (24 / columnCount)?int>

<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    label-width="80px"
    :size="formItemSize"
    label-position="right"
    @submit.prevent
  >
    <el-row :gutter="20" class="full-width-input">
<#list formColumns as column>
<#if !column.isPk>
      <el-col :span="${columnSpan}">
        <el-form-item label="${column.columnComment}" prop="${className}.${column.javaField}">
<#-- 树表父级字段特殊处理 -->
<#if genType?? && genType == "tree" && treeParentCodeJava?? && column.javaField == treeParentCodeJava>
          <el-cascader
            class="input-item"
            v-model="formEdit${className}.${column.javaField}.value"
            :clearable="true"
            placeholder="请选择${column.columnComment}"
            :size="formItemSize"
            :loading="formEdit${className}.${column.javaField}.impl.loading"
            :props="{ value: 'id', label: 'name', checkStrictly: true }"
            @visible-change="on${column.javaField?cap_first}VisibleChange"
            :options="formEdit${className}.${column.javaField}.impl.dropdownList"
          />
<#elseif column.dictType?? && column.dictType != "">
          <el-select
            class="input-item"
            v-model="formData.${className}.${column.javaField}"
            :clearable="true"
            placeholder="请选择${column.columnComment}"
          >
            <el-option
              v-for="item in ${column.javaField}DictList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
<#elseif column.javaType == "String">
<#if column.htmlType == "textarea">
          <el-input
            class="input-item"
            v-model="formData.${className}.${column.javaField}"
            type="textarea"
            :rows="3"
            :clearable="true"
            placeholder="${column.columnComment}"
<#if column.columnLength??>
            maxlength="${column.columnLength}"
</#if>
          />
<#else>
          <el-input
            class="input-item"
            v-model="formData.${className}.${column.javaField}"
            :clearable="true"
            placeholder="${column.columnComment}"
<#if column.columnLength??>
            maxlength="${column.columnLength}"
</#if>
          />
</#if>
<#elseif column.javaType == "Integer" || column.javaType == "Long">
          <el-input-number
            class="input-item"
            v-model="formData.${className}.${column.javaField}"
            :clearable="true"
            controls-position="right"
            placeholder="${column.columnComment}"
          />
<#elseif column.javaType == "BigDecimal">
          <el-input-number
            class="input-item"
            v-model="formData.${className}.${column.javaField}"
            :precision="2"
            :clearable="true"
            controls-position="right"
            placeholder="${column.columnComment}"
          />
<#elseif column.javaType == "Date">
          <el-date-picker
            class="input-item"
            v-model="formData.${className}.${column.javaField}"
            type="datetime"
            :clearable="true"
            placeholder="选择${column.columnComment}"
            style="width: 100%"
          />
<#elseif column.javaType == "Boolean">
          <el-switch
            class="input-item"
            v-model="formData.${className}.${column.javaField}"
          />
<#else>
          <el-input
            class="input-item"
            v-model="formData.${className}.${column.javaField}"
            :clearable="true"
            placeholder="${column.columnComment}"
          />
</#if>
        </el-form-item>
      </el-col>
</#if>
</#list>
      <el-col :span="24">
        <el-row class="no-scroll flex-box" type="flex" justify="end">
          <el-button :plain="true" @click="onCancel" :size="formItemSize">
            取消
          </el-button>
          <el-button
            type="primary"
            :size="formItemSize"
            :disabled="
              !(
                checkPermCodeExist('form${className}:fragment${className}:add') ||
                checkPermCodeExist('form${className}:fragment${className}:update')
              )
            "
            @click="onSaveClick()"
          >
            保存
          </el-button>
        </el-row>
      </el-col>
    </el-row>
  </el-form>
</template>

<script lang="ts">
export default {
  name: 'FormEdit${className}',
};
</script>

<script setup lang="ts">
import { computed, reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { DialogProp } from '@/components/Dialog/types';
import { usePermissions } from '@/common/hooks/usePermission';
import ${className}Controller from '@/api/${moduleName}/${className}Controller';
import { ANY_OBJECT } from '@/types/generic';
import { ${className} } from '@/types/${moduleName}/${className?lower_case}';
import { useLayoutStore } from '@/store';
<#list formColumns as column>
<#if column.dictType?? && column.dictType != "" && !column.isPk>
import { DictionaryController } from '@/api/system';
<#break>
</#if>
</#list>
<#-- 树表父级字段需要的导入 -->
<#if genType?? && genType == "tree">
import { findTreeNodePath } from '@/common/utils';
import { useDropdown } from '@/common/hooks/useDropdown';
import { DropdownOptions, ListData } from '@/common/types/list';
import { DictData } from '@/common/staticDict/types';
</#if>

const layoutStore = useLayoutStore();

const props = defineProps<{
  ${pkColumn.javaField}?: string;
  // 当使用Dialog.show弹出组件时，须定义该prop属性，以便对dialog进行回调
  dialog?: DialogProp<ANY_OBJECT>;
<#if genType?? && genType == "tree">
  // 树表新增子节点时的父级数据
  parentData?: ANY_OBJECT;
</#if>
}>();

const formItemSize = computed(() => {
  return layoutStore.defaultFormItemSize;
});

const { checkPermCodeExist } = usePermissions();

const form = ref();
const formData = reactive({
  ${className}: {
<#list formColumns as column>
<#if !column.isPk>
    ${column.javaField}: <#if column.javaType == "Boolean">false<#else>undefined</#if>,
</#if>
</#list>
  } as ${className},
});

<#-- 树表父级字段的下拉数据获取 -->
<#if genType?? && genType == "tree">
/**
 * ${treeParentCodeJava}下拉数据获取函数
 */
const load${treeParentCodeJava?cap_first}DropdownList = (): Promise<ListData<DictData>> => {
  return new Promise((resolve, reject) => {
    let params = {};
    ${className}Controller.listDict(params)
      .then(res => {
        // 转换数据格式为级联选择器需要的格式
        const dataList = (res.data || []).map((item: any) => ({
          id: item.id,
          name: item.name,
          parentId: item.parentId || null,
        }));
        resolve({
          dataList: dataList,
        });
      })
      .catch(e => {
        reject(e);
      });
  });
};

const ${treeParentCodeJava}DropdownOptions: DropdownOptions<ANY_OBJECT> = {
  loadData: load${treeParentCodeJava?cap_first}DropdownList,
  idKey: 'id',
  isTree: true,
};

const formEdit${className} = reactive({
  ${treeParentCodeJava}: {
    impl: useDropdown(${treeParentCodeJava}DropdownOptions),
    value: [] as string[],
  },
  isInit: false,
});
</#if>

<#list formColumns as column>
<#if column.dictType?? && column.dictType != "" && !column.isPk>
// ${column.columnComment}字典数据
const ${column.javaField}DictList = ref<Array<{id: any, name: string}>>([]);
</#if>
</#list>

const rules = {
<#list formColumns as column>
<#if !column.isPk && column.isRequired>
  '${className}.${column.javaField}': [{ required: true, message: '请输入${column.columnComment}', trigger: 'blur' }],
</#if>
</#list>
};

const isEdit = computed(() => {
  return props.${pkColumn.javaField} != null;
});

const onCancel = () => {
  if (props.dialog) {
    props.dialog.cancel();
  }
};

<#-- 树表父级字段的事件处理 -->
/**
 * 更新formEdit${className}
 */
const refreshFormEditData = () => {
<#if genType?? && genType == "tree">
  formEdit${className}.${treeParentCodeJava}.impl.onVisibleChange(true).then(() => {
    if (isEdit.value) {
      // 编辑模式：设置现有的父级值
      set${treeParentCodeJava?cap_first}Value(formData.${className}.${treeParentCodeJava});
    } else {
      // 新增模式：检查是否有父级数据
      if (props.parentData && props.parentData.${pkColumn.javaField}) {
        // 新增子节点：设置父级值
        set${treeParentCodeJava?cap_first}Value(props.parentData.${pkColumn.javaField});
      } else {
        // 新增根节点：清空父级值
        formEdit${className}.${treeParentCodeJava}.value = [];
      }
    }
    nextTick(() => {
      formEdit${className}.isInit = true;
    });
  });
</#if>
};

const load${className}Data = () => {
  if (isEdit.value) {
    let params = {
      ${pkColumn.javaField}: props.${pkColumn.javaField},
    };
    ${className}Controller.view(params)
      .then(res => {
        formData.${className} = { ...res.data };
<#if genType?? && genType == "tree">
        refreshFormEditData();
</#if>
      })
      .catch(e => {
        console.warn(e);
      });
  } else {
    // 新增模式，初始化默认数据
    formData.${className} = {
<#list formColumns as column>
<#if !column.isPk>
      ${column.javaField}: <#if column.javaType == "Boolean">false<#else>undefined</#if>,
</#if>
</#list>
    };

<#if genType?? && genType == "tree">
    // 如果是新增子节点，设置父级ID
    if (props.parentData && props.parentData.${pkColumn.javaField}) {
      formData.${className}.${treeParentCodeJava} = props.parentData.${pkColumn.javaField};
    }
    refreshFormEditData();
</#if>
  }
};

/**
 * 保存
 */
const onSaveClick = () => {
  form.value.validate((valid: boolean) => {
    if (!valid) return;

<#-- 树表父级字段验证 -->
<#if genType?? && genType == "tree">
    // 判断父节点是否为当前节点的子节点
    let ${treeParentCodeJava}Value = get${treeParentCodeJava?cap_first}Value();
    if (${treeParentCodeJava}Value != null && isEdit.value) {
      let path = findTreeNodePath(formEdit${className}.${treeParentCodeJava}.impl.dropdownList, ${treeParentCodeJava}Value);
      if (Array.isArray(path) && path.indexOf(props.${pkColumn.javaField}) !== -1) {
        ElMessage.error('上级不能为当前节点的子节点！');
        return;
      }
    }
    formData.${className}.${treeParentCodeJava} = ${treeParentCodeJava}Value;
</#if>

<#list formColumns as column>
<#if !column.isPk && column.isRequired>
    if (formData.${className}.${column.javaField} == null) {
      ElMessage.error('请求失败，发现必填参数为空！');
      return;
    }
</#if>
</#list>

    let params = {
      ${className?lower_case}Dto: {
        ${pkColumn.javaField}: props.${pkColumn.javaField},
<#list formColumns as column>
<#if !column.isPk>
        ${column.javaField}: formData.${className}.${column.javaField},
</#if>
</#list>
      },
    };

    let httpCall = isEdit.value ? ${className}Controller.update(params) : ${className}Controller.add(params);
    httpCall
      .then(res => {
        ElMessage.success('保存成功');
        if (props.dialog) {
          props.dialog.submit(res);
        }
      })
      .catch(e => {
        console.warn(e);
      });
  });
};

<#list formColumns as column>
<#if column.dictType?? && column.dictType != "" && !column.isPk>
/**
 * 加载${column.columnComment}字典数据
 */
const load${column.javaField?cap_first}Dict = () => {
  DictionaryController.dictGlobalDict({ dictCode: '${column.dictType}' })
    .then(res => {
      console.log('字典数据加载结果:', res);
      ${column.javaField}DictList.value = res.getList() || [];
    })
    .catch(e => {
      console.warn('加载${column.columnComment}字典失败:', e);
    });
};

</#if>
</#list>
<#if genType?? && genType == "tree">
/**
 * ${treeParentCodeJava}级联选择器展开和收起的事件
 */
const on${treeParentCodeJava?cap_first}VisibleChange = (show: boolean) => {
  if (show) {
    formEdit${className}.${treeParentCodeJava}.impl.onVisibleChange(show);
  }
};

/**
 * 从${treeParentCodeJava}级联列表中获取选中的节点
 */
const get${treeParentCodeJava?cap_first}Value = () => {
  let ${treeParentCodeJava}Id = null;
  if (Array.isArray(formEdit${className}.${treeParentCodeJava}.value) && formEdit${className}.${treeParentCodeJava}.value.length > 0) {
    ${treeParentCodeJava}Id = formEdit${className}.${treeParentCodeJava}.value[formEdit${className}.${treeParentCodeJava}.value.length - 1];
  }
  return ${treeParentCodeJava}Id;
};

/**
 * 根据${treeParentCodeJava}Id设置级联选择器的值
 */
const set${treeParentCodeJava?cap_first}Value = (${treeParentCodeJava}Id: string) => {
  if (${treeParentCodeJava}Id != null && formEdit${className}.${treeParentCodeJava}.impl.dropdownList.length > 0) {
    let path = findTreeNodePath(formEdit${className}.${treeParentCodeJava}.impl.dropdownList, ${treeParentCodeJava}Id, 'id', 'children');
    if (Array.isArray(path)) {
      formEdit${className}.${treeParentCodeJava}.value = path;
    }
  } else {
    formEdit${className}.${treeParentCodeJava}.value = [];
  }
};
</#if>

onMounted(() => {
  load${className}Data();
<#list formColumns as column>
<#if column.dictType?? && column.dictType != "" && !column.isPk>
  load${column.javaField?cap_first}Dict();
</#if>
</#list>
});
</script>
